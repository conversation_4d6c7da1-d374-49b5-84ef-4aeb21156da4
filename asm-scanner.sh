#!/bin/bash

# Attack Surface Management Scanner for Parrot OS
# Usage: ./asm-scanner.sh <target_domain>

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                 Parrot OS ASM Scanner v1.0                  ║"
echo "║              Attack Surface Management Tool                  ║"
echo "║                   For Authorized Testing Only                ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if target is provided
if [ $# -eq 0 ]; then
    echo -e "${RED}[ERROR] Please provide a target domain${NC}"
    echo "Usage: $0 <target_domain>"
    exit 1
fi

TARGET=$1
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="asm_${TARGET}_${TIMESTAMP}"

# Create output directory
mkdir -p "$OUTPUT_DIR"
cd "$OUTPUT_DIR"

echo -e "${GREEN}[INFO] Starting ASM assessment for: $TARGET${NC}"
echo -e "${GREEN}[INFO] Output directory: $OUTPUT_DIR${NC}"
echo -e "${YELLOW}[WARNING] Ensure you have authorization to test this target!${NC}"
read -p "Press Enter to continue or Ctrl+C to abort..."

# Initialize report
cat > "asm_report_${TARGET}.md" << EOF
# Attack Surface Management Report

**Target**: $TARGET  
**Assessment Date**: $(date)  
**Assessed By**: Parrot OS ASM Scanner  
**Report Generated**: $(date)

---

## Executive Summary

EOF

echo -e "${BLUE}[PHASE 1] Target Validation & Passive Reconnaissance${NC}"

# WHOIS lookup
echo -e "${GREEN}[1.1] WHOIS Lookup${NC}"
whois "$TARGET" > "whois_${TARGET}.txt" 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ WHOIS data collected${NC}"
else
    echo -e "${RED}✗ WHOIS lookup failed${NC}"
fi

# DNS enumeration
echo -e "${GREEN}[1.2] DNS Enumeration${NC}"
dig "$TARGET" ANY > "dns_${TARGET}.txt" 2>&1
host "$TARGET" >> "dns_${TARGET}.txt" 2>&1
echo -e "${GREEN}✓ DNS enumeration completed${NC}"

# Certificate transparency
echo -e "${GREEN}[1.3] Certificate Transparency Logs${NC}"
curl -s "https://crt.sh/?q=%.${TARGET}&output=json" | jq -r '.[].name_value' | sort -u > "crt_${TARGET}.txt" 2>/dev/null
if [ -s "crt_${TARGET}.txt" ]; then
    echo -e "${GREEN}✓ Certificate transparency data collected${NC}"
else
    echo -e "${YELLOW}⚠ No certificate transparency data found${NC}"
fi

echo -e "${BLUE}[PHASE 2] Active Subdomain Discovery${NC}"

# Subfinder
echo -e "${GREEN}[2.1] Running Subfinder${NC}"
if command -v subfinder &> /dev/null; then
    subfinder -d "$TARGET" -silent > "subfinder_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ Subfinder completed ($(wc -l < subfinder_${TARGET}.txt) subdomains)${NC}"
else
    echo -e "${YELLOW}⚠ Subfinder not found, skipping${NC}"
    touch "subfinder_${TARGET}.txt"
fi

# Assetfinder
echo -e "${GREEN}[2.2] Running Assetfinder${NC}"
if command -v assetfinder &> /dev/null; then
    assetfinder --subs-only "$TARGET" > "assetfinder_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ Assetfinder completed ($(wc -l < assetfinder_${TARGET}.txt) subdomains)${NC}"
else
    echo -e "${YELLOW}⚠ Assetfinder not found, skipping${NC}"
    touch "assetfinder_${TARGET}.txt"
fi

# Amass (passive mode)
echo -e "${GREEN}[2.3] Running Amass (passive)${NC}"
if command -v amass &> /dev/null; then
    timeout 300 amass enum -passive -d "$TARGET" > "amass_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ Amass completed ($(wc -l < amass_${TARGET}.txt) subdomains)${NC}"
else
    echo -e "${YELLOW}⚠ Amass not found, skipping${NC}"
    touch "amass_${TARGET}.txt"
fi

# Combine and deduplicate subdomains
echo -e "${GREEN}[2.4] Combining subdomain results${NC}"
cat "subfinder_${TARGET}.txt" "assetfinder_${TARGET}.txt" "amass_${TARGET}.txt" "crt_${TARGET}.txt" | \
    grep -E "^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.${TARGET//./\\.}$" | \
    sort -u > "all_subdomains_${TARGET}.txt"

TOTAL_SUBDOMAINS=$(wc -l < "all_subdomains_${TARGET}.txt")
echo -e "${GREEN}✓ Total unique subdomains found: $TOTAL_SUBDOMAINS${NC}"

# Validate live subdomains
echo -e "${GREEN}[2.5] Validating live subdomains${NC}"
if command -v httpx &> /dev/null; then
    cat "all_subdomains_${TARGET}.txt" | httpx -silent -timeout 10 > "live_subdomains_${TARGET}.txt" 2>&1
    LIVE_SUBDOMAINS=$(wc -l < "live_subdomains_${TARGET}.txt")
    echo -e "${GREEN}✓ Live subdomains: $LIVE_SUBDOMAINS${NC}"
else
    echo -e "${YELLOW}⚠ httpx not found, using all discovered subdomains${NC}"
    cp "all_subdomains_${TARGET}.txt" "live_subdomains_${TARGET}.txt"
fi

echo -e "${BLUE}[PHASE 3] Port Scanning & Service Detection${NC}"

# Nmap scan on main target
echo -e "${GREEN}[3.1] Nmap scan on main target${NC}"
nmap -sS -sV -sC -O -T4 --top-ports 1000 "$TARGET" -oA "nmap_${TARGET}" > /dev/null 2>&1
if [ -f "nmap_${TARGET}.nmap" ]; then
    echo -e "${GREEN}✓ Nmap scan completed${NC}"
else
    echo -e "${RED}✗ Nmap scan failed${NC}"
fi

# Quick port scan on live subdomains (top 100 ports)
echo -e "${GREEN}[3.2] Quick port scan on live subdomains${NC}"
if [ -s "live_subdomains_${TARGET}.txt" ]; then
    head -10 "live_subdomains_${TARGET}.txt" | while read subdomain; do
        if [ ! -z "$subdomain" ]; then
            clean_subdomain=$(echo "$subdomain" | sed 's|https\?://||' | cut -d'/' -f1)
            nmap -sS -T4 --top-ports 100 "$clean_subdomain" -oG "nmap_${clean_subdomain}.gnmap" > /dev/null 2>&1 &
        fi
    done
    wait
    echo -e "${GREEN}✓ Subdomain port scans completed${NC}"
fi

echo -e "${BLUE}[PHASE 4] SSL/TLS Certificate Analysis${NC}"

# SSL scan on main target
echo -e "${GREEN}[4.1] SSL/TLS Analysis${NC}"
if command -v sslscan &> /dev/null; then
    sslscan "$TARGET" > "sslscan_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ SSL scan completed${NC}"
else
    echo -e "${YELLOW}⚠ sslscan not found, using openssl${NC}"
    echo | openssl s_client -connect "${TARGET}:443" -servername "$TARGET" 2>/dev/null | \
        openssl x509 -text > "cert_${TARGET}.txt" 2>&1
fi

# testssl.sh if available
if command -v testssl.sh &> /dev/null; then
    echo -e "${GREEN}[4.2] Running testssl.sh${NC}"
    testssl.sh --quiet "$TARGET" > "testssl_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ testssl.sh completed${NC}"
fi

echo -e "${BLUE}[PHASE 5] Web Application Assessment${NC}"

# Technology identification
echo -e "${GREEN}[5.1] Technology Identification${NC}"
if command -v whatweb &> /dev/null; then
    whatweb "$TARGET" > "whatweb_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ Technology identification completed${NC}"
else
    echo -e "${YELLOW}⚠ whatweb not found, using curl${NC}"
    curl -I "https://$TARGET" > "headers_${TARGET}.txt" 2>&1
fi

# Directory enumeration (quick scan)
echo -e "${GREEN}[5.2] Directory Enumeration${NC}"
if command -v gobuster &> /dev/null; then
    if [ -f "/usr/share/wordlists/dirb/common.txt" ]; then
        timeout 300 gobuster dir -u "https://$TARGET" -w /usr/share/wordlists/dirb/common.txt -q > "gobuster_${TARGET}.txt" 2>&1
        echo -e "${GREEN}✓ Directory enumeration completed${NC}"
    else
        echo -e "${YELLOW}⚠ Wordlist not found, skipping directory enumeration${NC}"
    fi
else
    echo -e "${YELLOW}⚠ gobuster not found, skipping directory enumeration${NC}"
fi

# Nikto scan
echo -e "${GREEN}[5.3] Nikto Web Scanner${NC}"
if command -v nikto &> /dev/null; then
    timeout 300 nikto -h "$TARGET" -output "nikto_${TARGET}.txt" > /dev/null 2>&1
    echo -e "${GREEN}✓ Nikto scan completed${NC}"
else
    echo -e "${YELLOW}⚠ Nikto not found, skipping${NC}"
fi

# Nuclei vulnerability scan
echo -e "${GREEN}[5.4] Nuclei Vulnerability Scan${NC}"
if command -v nuclei &> /dev/null; then
    timeout 300 nuclei -u "$TARGET" -silent > "nuclei_${TARGET}.txt" 2>&1
    echo -e "${GREEN}✓ Nuclei scan completed${NC}"
else
    echo -e "${YELLOW}⚠ Nuclei not found, skipping${NC}"
fi

echo -e "${BLUE}[PHASE 6] Report Generation${NC}"

# Generate summary
echo -e "${GREEN}[6.1] Generating report summary${NC}"

# Count findings
OPEN_PORTS=$(grep -c "open" nmap_*.nmap 2>/dev/null || echo "0")
SSL_ISSUES=$(grep -c -i "weak\|vulnerable\|insecure" sslscan_*.txt testssl_*.txt 2>/dev/null || echo "0")
WEB_ISSUES=$(grep -c -i "warning\|error\|vulnerable" nikto_*.txt nuclei_*.txt 2>/dev/null || echo "0")

# Append to report
cat >> "asm_report_${TARGET}.md" << EOF

### Assessment Results Summary
- **Total Subdomains Discovered**: $TOTAL_SUBDOMAINS
- **Live Subdomains**: $LIVE_SUBDOMAINS  
- **Open Ports Identified**: $OPEN_PORTS
- **SSL/TLS Issues**: $SSL_ISSUES
- **Web Application Issues**: $WEB_ISSUES

### Risk Assessment
- **Overall Risk Score**: $(( (OPEN_PORTS + SSL_ISSUES + WEB_ISSUES) / 3 ))/10
- **Risk Level**: $(if [ $((OPEN_PORTS + SSL_ISSUES + WEB_ISSUES)) -gt 15 ]; then echo "HIGH"; elif [ $((OPEN_PORTS + SSL_ISSUES + WEB_ISSUES)) -gt 8 ]; then echo "MEDIUM"; else echo "LOW"; fi)

## Detailed Findings

### Discovered Subdomains
\`\`\`
$(cat all_subdomains_${TARGET}.txt)
\`\`\`

### Live Subdomains  
\`\`\`
$(cat live_subdomains_${TARGET}.txt)
\`\`\`

### Port Scan Results
\`\`\`
$(grep -h "open" nmap_*.nmap 2>/dev/null | head -20)
\`\`\`

### SSL/TLS Analysis Summary
\`\`\`
$(grep -h -i "cipher\|protocol\|certificate" sslscan_*.txt 2>/dev/null | head -10)
\`\`\`

### Web Technology Stack
\`\`\`
$(cat whatweb_${TARGET}.txt 2>/dev/null)
\`\`\`

### Security Issues Identified
\`\`\`
$(cat nuclei_${TARGET}.txt 2>/dev/null)
\`\`\`

## Files Generated
$(ls -la *.txt *.nmap *.gnmap *.xml 2>/dev/null | awk '{print "- " $9}')

---
**Report Generated**: $(date)  
**Tool**: Parrot OS ASM Scanner v1.0
EOF

echo -e "${GREEN}✓ Report generated: asm_report_${TARGET}.md${NC}"

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    Assessment Complete!                     ║"
echo "║                                                              ║"
echo "║  Report: asm_report_${TARGET}.md"
echo "║  Output Directory: $OUTPUT_DIR"
echo "║                                                              ║"
echo "║  Review the markdown report for detailed findings            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

cd ..
echo -e "${GREEN}[INFO] Assessment completed. Check the $OUTPUT_DIR directory for all results.${NC}"
